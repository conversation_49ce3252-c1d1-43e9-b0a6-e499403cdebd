# QuickActions Component

A reusable component for displaying quick action cards with icons, titles, descriptions, and call-to-action buttons.

## Features

- Responsive grid layout (1 column on mobile, 2 on tablet, 3 on desktop)
- Customizable title, actions, and styling
- Consistent design with the eBaDollar theme
- TypeScript support with proper interfaces

## Usage

```tsx
import QuickActions from "../../../components/QuickActions";

const MyComponent = () => {
  const actions = [
    {
      id: "create-listing",
      title: "Create a Listing",
      description: "Add your products and services to sell on eBaDollar",
      icon: CreateListingImg,
      link: "/member/listings/add",
      buttonText: "List Now",
    },
    {
      id: "view-marketplace",
      title: "View Marketplace",
      description: "Browse products and services for sale",
      icon: MarketplaceImg,
      link: "/member/marketplace",
      buttonText: "Shop Now",
      buttonColor: "bg-blue-600", // Optional custom color
    },
  ];

  return (
    <QuickActions
      title="Quick Actions"
      actions={actions}
      className="custom-class"
    />
  );
};
```

## Props

### QuickActionsProps

| Prop        | Type            | Default           | Description                                |
| ----------- | --------------- | ----------------- | ------------------------------------------ |
| `title`     | `string`        | `"Quick Actions"` | The title displayed above the action cards |
| `actions`   | `QuickAction[]` | Required          | Array of action objects                    |
| `className` | `string`        | `""`              | Additional CSS classes                     |

### QuickAction

| Property      | Type     | Required | Description                                               |
| ------------- | -------- | -------- | --------------------------------------------------------- |
| `id`          | `string` | Yes      | Unique identifier for the action                          |
| `title`       | `string` | Yes      | Title displayed on the card                               |
| `description` | `string` | Yes      | Description text below the title                          |
| `icon`        | `string` | Yes      | URL or import path to the icon image                      |
| `link`        | `string` | Yes      | Navigation link for the button                            |
| `buttonText`  | `string` | Yes      | Text displayed on the button                              |
| `buttonColor` | `string` | No       | Custom CSS class for button color (defaults to red theme) |

## Styling

The component uses Tailwind CSS classes and follows the eBaDollar design system:

- White background cards with rounded corners
- Consistent spacing and typography
- Hover effects on buttons
- Responsive grid layout
- Theme colors: `#0F2C59` for titles, `#E63946` for buttons

## Examples

### Basic Usage

```tsx
<QuickActions
  actions={[
    {
      id: "action-1",
      title: "My Action",
      description: "Description here",
      icon: "/path/to/icon.png",
      link: "/my-route",
      buttonText: "Go",
    },
  ]}
/>
```

### Custom Styling

```tsx
<QuickActions
  title="Custom Title"
  actions={actions}
  className="mt-8 custom-spacing"
/>
```

### Custom Button Colors

```tsx
const actions = [
  {
    id: "primary-action",
    title: "Primary Action",
    description: "Main action",
    icon: primaryIcon,
    link: "/primary",
    buttonText: "Primary",
    buttonColor: "bg-blue-600",
  },
  {
    id: "secondary-action",
    title: "Secondary Action",
    description: "Secondary action",
    icon: secondaryIcon,
    link: "/secondary",
    buttonText: "Secondary",
    buttonColor: "bg-green-600",
  },
];
```
