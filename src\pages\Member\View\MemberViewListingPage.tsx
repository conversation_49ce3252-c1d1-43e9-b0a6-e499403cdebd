import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { EditIcon, StarIcon } from "../../../assets/svgs";

interface ISellerInfo {
  name: string;
  rating: number;
  reviews: number;
  member_since: string;
  credit_score: string;
  location: string;
  verified: boolean;
}

interface IBookingOptions {
  service_location: string;
  availability: string;
  time_slots: string;
  payment: string;
}

interface ISellerData {
  first_name?: string;
  last_name?: string;
  phone?: string;
  referral_code?: string;
  referral_type?: string;
  terms_accepted?: boolean;
  privacy_accepted?: boolean;
  referrer_name?: string;
}

interface ISeller {
  id: number;
  name: string;
  email: string;
  data: ISellerData | null;
  status: number;
  verify: number;
  rating: number;
  location: string;
  createdAt: string;
}

interface IListing {
  id: number;
  name: string;
  seller: string | ISeller;
  seller_id?: number;
  price: string;
  discountPrice?: number;
  quantity?: number;
  listing_type: string;
  status: string;
  description?: string;
  image?: string;
  images?: string[];
  video?: string;
  videos?: string[];
  category?: string;
  category_id?: number;
  created_at: string;
  updated_at: string;
  createdAt: string;
  updatedAt: string;
  rating?: number;
  sponsored?: boolean;
  type?: string;
  tags?: string[];
  seller_info?: ISellerInfo;
  availableSizes?: { short: string; long: string }[];
  price_per_hour?: boolean;
  booking_options?: IBookingOptions;
  isFavorited?: boolean;
  viewCount?: number;
  favoriteCount?: number;
  inquiryCount?: number;
  offerCount?: number;
}

const MemberViewListingPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [listing, setListing] = useState<IListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [similarListings, setSimilarListings] = useState<IListing[]>([]);
  const [loadingSimilar, setLoadingSimilar] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [favoritingLoading, setFavoritingLoading] = useState(false);

  // Zoom functionality state
  const [showZoomModal, setShowZoomModal] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageRef, setImageRef] = useState<HTMLImageElement | null>(null);

  // Video functionality state
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoRef, setVideoRef] = useState<HTMLVideoElement | null>(null);

  const { sdk } = useSDK();

  useEffect(() => {
    if (id) {
      fetchListing();
    }
  }, [id]);

  const fetchListing = async () => {
    setLoading(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/listings/${id}`,
        method: "GET",
      });

      if (!response.error && response.data) {
        setListing(response.data);
        setIsFavorited(response.data.isFavorited || false);

        // Fetch similar listings after setting the main listing
        setTimeout(() => {
          fetchSimilarListings();
        }, 100);
      } else {
        console.error("Error fetching listing:", response.message);
        setListing(null);
      }
    } catch (error) {
      console.error("Error fetching listing:", error);
      setListing(null);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    if (!listing) return;

    const newStatus = listing.status === "active" ? "draft" : "active";

    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/listings/${listing.id}`,
        method: "PUT",
        body: {
          name: listing.name,
          description: listing.description,
          price: listing.price,
          listing_type: listing.listing_type,
          status: newStatus,
          images: listing.images,
          category_id: listing.category_id,
        },
      });

      if (!response.error) {
        setListing((prev) => (prev ? { ...prev, status: newStatus } : null));
        alert(
          `Listing ${newStatus === "active" ? "activated" : "deactivated"} successfully!`
        );
      } else {
        console.error("Error updating listing status:", response.message);
        alert("Failed to update listing status. Please try again.");
      }
    } catch (error) {
      console.error("Error updating listing status:", error);
      alert("Failed to update listing status. Please try again.");
    }
  };

  const handleDelete = async () => {
    if (!listing) return;

    if (
      !confirm(
        "Are you sure you want to delete this listing? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/listings/${listing.id}`,
        method: "DELETE",
      });

      if (!response.error) {
        alert("Listing deleted successfully!");
        navigate("/member/listings");
      } else {
        console.error("Error deleting listing:", response.message);
        alert("Failed to delete listing. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting listing:", error);
      alert("Failed to delete listing. Please try again.");
    }
  };

  const handleToggleFavorite = async () => {
    if (!listing) return;

    setFavoritingLoading(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/favorites/${listing.id}/toggle`,
        method: "POST",
      });

      if (!response.error && response.data) {
        setIsFavorited(response.data.isFavorited);
        // Show success message
        const action = response.data.action;
        alert(
          `Listing ${action === "added" ? "added to" : "removed from"} favorites!`
        );
      } else {
        console.error("Error toggling favorite:", response.message);
        alert(
          response.message ||
            "Failed to update favorite status. Please try again."
        );
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      alert("Failed to update favorite status. Please try again.");
    } finally {
      setFavoritingLoading(false);
    }
  };

  const handleToggleSimilarFavorite = async (listingId: number) => {
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/favorites/${listingId}/toggle`,
        method: "POST",
      });

      if (!response.error && response.data) {
        // Update the similar listings state
        setSimilarListings((prev) =>
          prev.map((item) =>
            item.id === listingId
              ? { ...item, isFavorited: response.data.isFavorited }
              : item
          )
        );

        const action = response.data.action;
        alert(
          `Listing ${action === "added" ? "added to" : "removed from"} favorites!`
        );
      } else {
        console.error("Error toggling favorite:", response.message);
        alert(
          response.message ||
            "Failed to update favorite status. Please try again."
        );
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      alert("Failed to update favorite status. Please try again.");
    }
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  const fetchSimilarListings = async () => {
    if (!listing) return;

    setLoadingSimilar(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/marketplace/listings/${listing.id}/similar`,
        method: "GET",
        params: { limit: 6 },
      });

      if (!response.error && response.data) {
        setSimilarListings(response.data);
      } else {
        console.error("Error fetching similar listings:", response.message);
        setSimilarListings([]);
      }
    } catch (error) {
      console.error("Error fetching similar listings:", error);
      setSimilarListings([]);
    } finally {
      setLoadingSimilar(false);
    }
  };

  const getSimilarListings = () => {
    return similarListings.slice(0, 3);
  };

  const getProductMedia = () => {
    if (!listing) return [];

    const media: Array<{
      type: "image" | "video";
      url: string;
      index: number;
    }> = [];
    let index = 0;

    // Add main image if it exists
    if (
      listing.image &&
      listing.image.trim() !== "" &&
      listing.image.trim() !== "/api/placeholder/300/200"
    ) {
      media.push({ type: "image", url: listing.image, index: index++ });
    }

    // Add additional images if they exist
    if (listing.images && Array.isArray(listing.images)) {
      listing.images.forEach((img) => {
        if (
          img &&
          img.trim() !== "" &&
          img.trim() !== "/api/placeholder/300/200"
        ) {
          media.push({ type: "image", url: img, index: index++ });
        }
      });
    }

    // Add main video if it exists
    if (listing.video && listing.video.trim() !== "") {
      media.push({ type: "video", url: listing.video, index: index++ });
    }

    // Add additional videos if they exist
    if (listing.videos && Array.isArray(listing.videos)) {
      listing.videos.forEach((video) => {
        if (video && video.trim() !== "") {
          media.push({ type: "video", url: video, index: index++ });
        }
      });
    }

    // If no media found, return a default image
    if (media.length === 0) {
      media.push({
        type: "image",
        url: "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=800",
        index: 0,
      });
    }

    return media;
  };

  const getProductImages = () => {
    return getProductMedia()
      .filter((item) => item.type === "image")
      .map((item) => item.url);
  };

  // Zoom functionality handlers
  const handleImageClick = () => {
    setShowZoomModal(true);
    setZoomLevel(1);
    setZoomPosition({ x: 0, y: 0 });
  };

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev * 1.5, 5));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => Math.max(prev / 1.5, 0.5));
  };

  const handleResetZoom = () => {
    setZoomLevel(1);
    setZoomPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - zoomPosition.x,
        y: e.clientY - zoomPosition.y,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoomLevel > 1) {
      setZoomPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoomLevel((prev) => Math.max(0.5, Math.min(5, prev * delta)));
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!showZoomModal) return;

    switch (e.key) {
      case "Escape":
        setShowZoomModal(false);
        break;
      case "ArrowLeft":
        setSelectedImageIndex((prev) => Math.max(0, prev - 1));
        handleResetZoom();
        break;
      case "ArrowRight":
        setSelectedImageIndex((prev) =>
          Math.min(getProductMedia().length - 1, prev + 1)
        );
        handleResetZoom();
        break;
      case "+":
      case "=":
        handleZoomIn();
        break;
      case "-":
        handleZoomOut();
        break;
      case "0":
        handleResetZoom();
        break;
    }
  };

  // Keyboard event listener
  useEffect(() => {
    if (showZoomModal) {
      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";
    } else {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [showZoomModal, selectedImageIndex, zoomLevel]);

  // Video functionality handlers
  const handleVideoPlay = () => {
    setIsVideoPlaying(true);
  };

  const handleVideoPause = () => {
    setIsVideoPlaying(false);
  };

  const handleVideoEnded = () => {
    setIsVideoPlaying(false);
  };

  const handleVideoClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const currentMedia = getProductMedia()[selectedImageIndex];
    if (currentMedia?.type === "video") {
      if (isVideoPlaying) {
        videoRef?.pause();
      } else {
        videoRef?.play();
      }
    }
  };

  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </MemberWrapper>
    );
  }

  if (!listing) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Listing not found</div>
            <InteractiveButton
              onClick={() => navigate("/member/listings")}
              className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
            >
              Back to My Listings
            </InteractiveButton>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="bg-[#0D3166] min-h-screen text-white">
        <div className="container mx-auto px-6 py-8">
          {/* Back Button */}
          <button
            onClick={() => navigate("/member/listings")}
            className="flex items-center gap-2 text-sm mb-6 hover:text-gray-300"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            My Listing Details
          </button>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-8">
              {/* Product Header */}
              <div>
                <h1 className="text-3xl font-bold mb-2">{listing.name}</h1>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>
                    Listed{" "}
                    {new Date(
                      listing.createdAt || listing.created_at
                    ).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-2 bg-gray-700 px-2 py-1 rounded">
                    <span>Type:</span>
                    <span className="font-semibold">
                      {listing.listing_type || listing.type}
                    </span>
                  </span>
                  {listing.viewCount !== undefined && (
                    <span className="flex items-center gap-2 bg-blue-700 px-2 py-1 rounded">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                      <span>{listing.viewCount} views</span>
                    </span>
                  )}
                  {listing.favoriteCount !== undefined && (
                    <span className="flex items-center gap-2 bg-red-700 px-2 py-1 rounded">
                      <svg
                        className="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span>{listing.favoriteCount} favorites</span>
                    </span>
                  )}
                </div>
              </div>

              {/* Media Gallery */}
              <div>
                <div className="relative mb-4">
                  {(() => {
                    const currentMedia = getProductMedia()[selectedImageIndex];
                    if (!currentMedia) {
                      return (
                        <div className="w-full h-96 flex items-center justify-center bg-gray-100 rounded-lg">
                          <div className="text-center">
                            <svg
                              className="w-16 h-16 text-gray-400 mx-auto mb-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                            <p className="text-gray-500 text-lg font-medium">
                              No media found
                            </p>
                          </div>
                        </div>
                      );
                    }

                    if (currentMedia.type === "video") {
                      return (
                        <div className="relative">
                          <video
                            ref={(el) => setVideoRef(el)}
                            src={currentMedia.url}
                            className="w-full h-auto object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                            onClick={handleVideoClick}
                            onPlay={handleVideoPlay}
                            onPause={handleVideoPause}
                            onEnded={handleVideoEnded}
                            controls
                            preload="metadata"
                          />
                          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                            <svg
                              className="w-4 h-4 inline mr-1"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                            </svg>
                            Video
                          </div>
                        </div>
                      );
                    }

                    return (
                      <img
                        src={currentMedia.url}
                        alt={listing.name}
                        className="w-full h-auto object-cover rounded-lg cursor-zoom-in hover:opacity-90 transition-opacity"
                        onClick={handleImageClick}
                        style={{ cursor: "zoom-in" }}
                      />
                    );
                  })()}
                  <div className="absolute top-4 left-4">
                    {listing.sponsored && (
                      <span className="bg-yellow-400 text-black px-3 py-1 rounded text-xs font-bold flex items-center gap-1">
                        <StarIcon className="w-3 h-3" />
                        Sponsored
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() =>
                      setSelectedImageIndex(Math.max(0, selectedImageIndex - 1))
                    }
                    className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2"
                  >
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() =>
                      setSelectedImageIndex(
                        Math.min(
                          getProductMedia().length - 1,
                          selectedImageIndex + 1
                        )
                      )
                    }
                    className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2"
                  >
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
                    {selectedImageIndex + 1} / {getProductMedia().length}
                  </div>
                  <div className="absolute bottom-4 right-4 text-sm flex items-center gap-1 bg-black bg-opacity-50 px-2 py-1 rounded-full">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                    Click to zoom
                  </div>
                </div>
                <div className="flex gap-2">
                  {getProductMedia().map((media, index) => (
                    <div
                      key={index}
                      className={`w-20 h-20 rounded-md cursor-pointer border-2 ${
                        selectedImageIndex === index
                          ? "border-[#F52D2A]"
                          : "border-transparent"
                      }`}
                      onClick={() => setSelectedImageIndex(index)}
                    >
                      {media.type === "video" ? (
                        <div className="relative w-full h-full">
                          <video
                            src={media.url}
                            className="w-full h-full object-cover rounded-md"
                            preload="metadata"
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-md">
                            <svg
                              className="w-6 h-6 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                            </svg>
                          </div>
                        </div>
                      ) : (
                        <img
                          src={media.url}
                          alt={`thumbnail ${index}`}
                          className="w-full h-full object-cover rounded-md"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h2 className="text-xl font-bold mb-4">Description</h2>
                <div
                  className="text-sm space-y-4 whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: listing.description
                      ? listing.description.replace(
                          /Key specifications:|This laptop is in excellent condition|This is perfect for gamers/g,
                          (match) => `<strong>${match}</strong>`
                        )
                      : "",
                  }}
                />
                <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center">
                  <div className="flex gap-4">
                    <span>
                      Listed on{" "}
                      {new Date(
                        listing.createdAt || listing.created_at
                      ).toLocaleDateString()}
                    </span>
                    <span>
                      Updated on{" "}
                      {new Date(
                        listing.updatedAt || listing.updated_at
                      ).toLocaleDateString()}
                    </span>
                    <span>{listing.category?.split(">")[0].trim()}</span>
                  </div>
                </div>
              </div>

              {/* Delivery & Shipping or Booking Options */}
              {listing.type === "Service" && listing.booking_options ? (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">Booking Options</h2>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Service Location</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.service_location}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Availability</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.availability}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Time Slots</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.time_slots}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Payment</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.payment}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">
                    Delivery & Shipping
                  </h2>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 1: eBa Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Trackable delivery managed by the platform.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 2: Other Local Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller delivers within the same city/region.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-red-500 mt-1">✗</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 3: International Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller ships via FedEx, DHL, etc. to other countries.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 4: Pickup</h4>
                        <p className="text-xs text-gray-500">
                          Buyer collects the item from seller's location.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 5: Meet-up</h4>
                        <p className="text-xs text-gray-500">
                          Buyer and seller meet in a public place to exchange
                          the item.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Similar Listings */}
              <div>
                <h2 className="text-xl font-bold mb-4">Similar Listings</h2>
                {loadingSimilar ? (
                  <div className="flex justify-center py-8">
                    <MkdLoader />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {getSimilarListings().map((item) => (
                      <Link
                        key={item.id}
                        to={`/member/marketplace/listings/${item.id}`}
                        className="bg-white rounded-lg text-black overflow-hidden relative hover:shadow-lg transition-shadow"
                      >
                        {(() => {
                          // Check for video first, then image
                          if (item.video && item.video.trim() !== "") {
                            return (
                              <div className="relative w-full h-40">
                                <video
                                  src={item.video}
                                  className="w-full h-full object-cover"
                                  preload="metadata"
                                />
                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                  <svg
                                    className="w-8 h-8 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                                  </svg>
                                </div>
                              </div>
                            );
                          }

                          if (
                            item.image &&
                            item.image.trim() !== "" &&
                            item.image.trim() !== "/api/placeholder/300/200"
                          ) {
                            return (
                              <img
                                src={item.image}
                                alt={item.name}
                                className="w-full h-40 object-cover"
                              />
                            );
                          }

                          return (
                            <div className="w-full h-40 flex items-center justify-center bg-gray-100">
                              <div className="text-center">
                                <svg
                                  className="w-12 h-12 text-gray-400 mx-auto mb-2"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                  />
                                </svg>
                                <p className="text-gray-500 text-sm font-medium">
                                  No media found
                                </p>
                              </div>
                            </div>
                          );
                        })()}
                        <div className="p-4">
                          <div className="flex gap-2 mb-2">
                            {item.sponsored && (
                              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                ⭐ Sponsored
                              </span>
                            )}
                            {item.id === 2 && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                New
                              </span>
                            )}
                          </div>
                          <h4 className="font-semibold text-sm mb-2">
                            {item.name}
                          </h4>
                          <p className="text-[#F52D2A] font-bold text-lg mb-2">
                            eBa$ {item.price}
                          </p>
                          <p className="text-xs text-gray-500 mb-3 truncate">
                            {item.description}
                          </p>
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-1">
                              {renderStars(item.rating)}
                            </div>
                            <div className="flex items-center gap-1">
                              <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                              <span>
                                {typeof item.seller === "string"
                                  ? item.seller
                                  : item.seller?.name ||
                                    (item.seller?.data?.first_name &&
                                    item.seller?.data?.last_name
                                      ? `${item.seller.data.first_name} ${item.seller.data.last_name}`
                                      : item.seller?.data?.first_name ||
                                        "Unknown Seller")}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
                            <span>{item.seller_info?.location}</span>
                            <span>Added {item.created_at}</span>
                          </div>
                        </div>
                        <button
                          className="absolute top-2 right-2 bg-white rounded-full p-1.5 shadow hover:bg-gray-50 transition-colors"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleToggleSimilarFavorite(item.id);
                          }}
                          title={
                            item.isFavorited
                              ? "Remove from favorites"
                              : "Add to favorites"
                          }
                        >
                          <svg
                            className={`w-4 h-4 transition-colors ${
                              item.isFavorited
                                ? "text-red-500 fill-current"
                                : "text-gray-600"
                            }`}
                            fill={item.isFavorited ? "currentColor" : "none"}
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Right Column (Sticky) */}
            <div className="lg:sticky top-8 self-start space-y-6">
              {/* Purchase Card */}
              <div className="bg-white text-black p-6 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <p className="text-3xl font-bold text-[#0D3166]">
                    eBa$ {listing.price}
                    {listing.price_per_hour && "/hr"}
                  </p>
                  <button
                    onClick={handleToggleFavorite}
                    disabled={favoritingLoading}
                    className={`p-1 rounded-full transition-colors ${
                      favoritingLoading
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-gray-100"
                    }`}
                    title={
                      isFavorited ? "Remove from favorites" : "Add to favorites"
                    }
                  >
                    <svg
                      className={`w-6 h-6 transition-colors ${
                        isFavorited
                          ? "text-red-500 fill-current"
                          : "text-gray-600"
                      }`}
                      fill={isFavorited ? "currentColor" : "none"}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </button>
                </div>

                {listing.quantity && (
                  <div className="text-sm text-gray-600 mb-2">
                    Quantity: {listing.quantity}
                  </div>
                )}
                <div className="text-sm text-gray-600 mb-2">
                  Type: {listing.listing_type || listing.type}
                </div>
                <div className="flex items-center gap-2 mb-4">
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      listing.status === "active"
                        ? "bg-green-100 text-green-800"
                        : listing.status === "sold"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {listing.status.charAt(0).toUpperCase() +
                      listing.status.slice(1)}
                  </span>
                </div>

                {/* Statistics */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3">
                    Listing Statistics
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {listing.viewCount || 0}
                      </div>
                      <div className="text-sm text-gray-600">Views</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {listing.favoriteCount || 0}
                      </div>
                      <div className="text-sm text-gray-600">Favorites</div>
                    </div>
                    {listing.inquiryCount !== undefined && (
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {listing.inquiryCount}
                        </div>
                        <div className="text-sm text-gray-600">Inquiries</div>
                      </div>
                    )}
                    {listing.offerCount !== undefined && (
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {listing.offerCount}
                        </div>
                        <div className="text-sm text-gray-600">Offers</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Management Actions */}
                <div className="space-y-3">
                  <div className="flex gap-3">
                    <Link
                      to={`/member/listings/edit/${listing.id}`}
                      className="flex-1 bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] text-center flex items-center justify-center gap-2"
                    >
                      <EditIcon className="w-4 h-4" />
                      Edit Listing
                    </Link>

                    <InteractiveButton
                      onClick={handleToggleStatus}
                      className={`flex-1 py-3 px-6 rounded-md font-medium ${
                        listing.status === "active"
                          ? "bg-yellow-500 text-white hover:bg-yellow-600"
                          : "bg-green-500 text-white hover:bg-green-600"
                      }`}
                    >
                      {listing.status === "active" ? "Deactivate" : "Activate"}
                    </InteractiveButton>
                  </div>

                  <InteractiveButton
                    onClick={handleDelete}
                    className="w-full py-3 px-6 border border-red-500 text-red-500 rounded-md hover:bg-red-500 hover:text-white transition-colors"
                  >
                    Delete Listing
                  </InteractiveButton>

                  {listing.status === "active" && (
                    <Link
                      to={`/member/marketplace/${listing.id}`}
                      className="block w-full py-3 px-6 border border-[#e53e3e] text-[#e53e3e] rounded-md hover:bg-[#e53e3e] hover:text-white transition-colors text-center"
                    >
                      View in Marketplace
                    </Link>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    <div>
                      Created:{" "}
                      {new Date(
                        listing.createdAt || listing.created_at
                      ).toLocaleDateString()}
                    </div>
                    <div>
                      Updated:{" "}
                      {new Date(
                        listing.updatedAt || listing.updated_at
                      ).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Zoom Modal */}
        {showZoomModal && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
            <div className="relative w-full h-full flex items-center justify-center">
              <button
                onClick={() => setShowZoomModal(false)}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>

              <div className="absolute top-4 left-4 text-white text-sm z-10">
                <div className="bg-black bg-opacity-50 px-3 py-1 rounded">
                  {selectedImageIndex + 1} / {getProductMedia().length}
                </div>
              </div>

              <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
                <button
                  onClick={handleZoomOut}
                  className="bg-black bg-opacity-50 text-white px-3 py-1 rounded hover:bg-opacity-75"
                >
                  -
                </button>
                <button
                  onClick={handleResetZoom}
                  className="bg-black bg-opacity-50 text-white px-3 py-1 rounded hover:bg-opacity-75"
                >
                  Reset
                </button>
                <button
                  onClick={handleZoomIn}
                  className="bg-black bg-opacity-50 text-white px-3 py-1 rounded hover:bg-opacity-75"
                >
                  +
                </button>
              </div>

              <div
                className="relative overflow-hidden cursor-move"
                style={{ width: "90vw", height: "90vh" }}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onWheel={handleWheel}
              >
                {(() => {
                  const currentMedia = getProductMedia()[selectedImageIndex];
                  if (currentMedia?.type === "image") {
                    return (
                      <img
                        src={currentMedia.url}
                        alt={listing.name}
                        className="max-w-none"
                        style={{
                          transform: `scale(${zoomLevel}) translate(${zoomPosition.x}px, ${zoomPosition.y}px)`,
                          transformOrigin: "center center",
                        }}
                        draggable={false}
                      />
                    );
                  }
                  return null;
                })()}
              </div>

              <button
                onClick={() =>
                  setSelectedImageIndex(Math.max(0, selectedImageIndex - 1))
                }
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded hover:bg-opacity-75"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              <button
                onClick={() =>
                  setSelectedImageIndex(
                    Math.min(
                      getProductMedia().length - 1,
                      selectedImageIndex + 1
                    )
                  )
                }
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded hover:bg-opacity-75"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </MemberWrapper>
  );
};

export default MemberViewListingPage;
