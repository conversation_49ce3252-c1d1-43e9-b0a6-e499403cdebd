import React, { useState } from "react";
import {
  useStripe,
  useElements,
  CardElement,
  Elements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import MkdSDK from "../../utils/MkdSDK";
import useCustomerPaymentMethods from "../../hooks/useCustomerPaymentMethods";

// Initialize Stripe
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
console.log(
  "Stripe Publishable Key:",
  stripePublishableKey ? "Set" : "Not set"
);

const stripePromise = loadStripe(stripePublishableKey || "");

interface BillingDetails {
  name: string;
  address: {
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

interface StripePaymentFormProps {
  onSuccess: (result: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: "add_payment_method" | "process_payment";
  amount?: number;
  currency?: string;
  paymentMethodId?: string;
}

const StripePaymentForm: React.FC<StripePaymentFormProps> = ({
  onSuccess,
  onCancel,
  isLoading = false,
  mode = "add_payment_method",
  amount = 0,
  currency = "usd",
  paymentMethodId,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const queryClient = useQueryClient();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] =
    useState<string>("");
  const [cardReady, setCardReady] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);

  // Get existing payment methods for process_payment mode
  const { paymentMethods, isLoadingPaymentMethods } =
    useCustomerPaymentMethods();

  const [billingDetails, setBillingDetails] = useState<BillingDetails>({
    name: "",
    address: {
      line1: "",
      city: "",
      state: "",
      postal_code: "",
      country: "US",
    },
  });

  const sdk = new MkdSDK();

  // Mutation for attaching PaymentMethod to customer
  const attachPaymentMethod = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      return sdk.attachPaymentMethodToCustomer({
        payment_method_id: paymentMethodId,
      });
    },
    onSuccess: () => {
      // Invalidate payment methods query to refresh the list
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
  });

  // Mutation for processing payment
  const processPayment = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      return sdk.processStripeTopUp({
        amount: amount,
        payment_method_id: paymentMethodId,
        currency: currency,
      });
    },
    onSuccess: () => {
      // Invalidate account balance query to refresh the balance
      queryClient.invalidateQueries({ queryKey: ["accountBalance"] });
    },
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    setProcessing(true);
    setError(null);

    try {
      let finalPaymentMethodId: string;

      if (paymentMethodId) {
        // Use provided payment method ID
        finalPaymentMethodId = paymentMethodId;
      } else if (mode === "process_payment" && selectedPaymentMethodId) {
        // Use existing payment method from modal selection
        finalPaymentMethodId = selectedPaymentMethodId;
      } else {
        // Create new payment method using Stripe Elements
        if (!stripe || !elements) {
          setError("Stripe has not loaded yet. Please try again.");
          return;
        }

        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          setError("Card element not found. Please refresh and try again.");
          console.error("CardElement not found in elements");
          return;
        }

        console.log("CardElement found, creating payment method...");
        console.log("Billing details:", billingDetails);

        const { error: stripeError, paymentMethod } =
          await stripe.createPaymentMethod({
            type: "card",
            card: cardElement,
            billing_details: {
              name: billingDetails.name,
              address: {
                line1: billingDetails.address.line1,
                city: billingDetails.address.city,
                state: billingDetails.address.state,
                postal_code: billingDetails.address.postal_code,
                country: billingDetails.address.country,
              },
            },
          });

        if (stripeError) {
          setError(stripeError.message || "Failed to create payment method");
          return;
        }

        if (!paymentMethod) {
          setError("Failed to create payment method");
          return;
        }

        finalPaymentMethodId = paymentMethod.id;
      }

      if (mode === "add_payment_method") {
        // Attach PaymentMethod to customer
        const attachResult =
          await attachPaymentMethod.mutateAsync(finalPaymentMethodId);

        if (attachResult.error) {
          setError(attachResult.message || "Failed to attach payment method");
          return;
        }

        // Success - call the onSuccess callback
        onSuccess({ id: finalPaymentMethodId });
      } else if (mode === "process_payment") {
        // Process payment directly
        const paymentResult =
          await processPayment.mutateAsync(finalPaymentMethodId);

        if (paymentResult.error) {
          setError(paymentResult.message || "Failed to process payment");
          return;
        }

        // Success - call the onSuccess callback with payment result
        onSuccess(paymentResult.data);
      }
    } catch (err: any) {
      console.error("Payment processing error:", err);
      setError(
        err.message ||
          `Failed to ${mode === "add_payment_method" ? "add payment method" : "process payment"}`
      );
    } finally {
      setProcessing(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#424770",
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: "antialiased",
        lineHeight: "24px",
        "::placeholder": {
          color: "#aab7c4",
        },
        ":-webkit-autofill": {
          color: "#424770",
        },
      },
      invalid: {
        color: "#9e2146",
        iconColor: "#9e2146",
      },
      complete: {
        color: "#424770",
      },
    },
    hidePostalCode: false,
    disabled: false,
  };

  const formatCardDisplay = (paymentMethod: any) => {
    if (paymentMethod.card) {
      const brand =
        paymentMethod.card.brand.charAt(0).toUpperCase() +
        paymentMethod.card.brand.slice(1);
      return `${brand} •••• ${paymentMethod.card.last4}`;
    }
    return "Card";
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {mode === "process_payment" ? (
        // Show existing payment methods for payment processing
        <>
          {/* Amount Display */}
          <div className="mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Top-up Amount</p>
                <p className="text-2xl font-bold text-[#0F2C59]">
                  ${amount.toFixed(2)} USD
                </p>
                <p className="text-sm text-gray-500">
                  ≈ {(amount / 1.45).toFixed(2)} EBA$
                </p>
              </div>
            </div>
          </div>

          {/* Selected Payment Method Display - Show when paymentMethodId is provided */}
          {paymentMethodId && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Payment Method
              </label>
              <div className="p-3 border border-gray-300 rounded-lg bg-gray-50">
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">💳</span>
                  <span className="text-sm font-medium">
                    {paymentMethods.find((m) => m.id === paymentMethodId)
                      ? formatCardDisplay(
                          paymentMethods.find((m) => m.id === paymentMethodId)!
                        )
                      : `Payment Method ${paymentMethodId}`}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Payment Method Selection - Only show if no paymentMethodId is provided */}
          {!paymentMethodId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Payment Method
              </label>

              {isLoadingPaymentMethods ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#0F2C59] mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">
                    Loading payment methods...
                  </p>
                </div>
              ) : paymentMethods.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500 mb-2">
                    No payment methods found
                  </p>
                  <p className="text-xs text-gray-400">
                    Please add a payment method first
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {paymentMethods.map((method) => (
                    <label
                      key={method.id}
                      className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    >
                      <input
                        type="radio"
                        name="paymentMethod"
                        value={method.id}
                        checked={selectedPaymentMethodId === method.id}
                        onChange={(e) =>
                          setSelectedPaymentMethodId(e.target.value)
                        }
                        className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                      />
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="text-blue-600 mr-2">💳</span>
                          <span className="text-sm font-medium">
                            {formatCardDisplay(method)}
                          </span>
                        </div>
                        {method.card && (
                          <p className="text-xs text-gray-500 ml-6">
                            Expires{" "}
                            {method.card.exp_month.toString().padStart(2, "0")}/
                            {method.card.exp_year}
                          </p>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>
          )}
        </>
      ) : (
        // Show card input form for adding new payment method
        <>
          {/* Cardholder Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Cardholder Name
            </label>
            <input
              type="text"
              value={billingDetails.name}
              onChange={(e) =>
                setBillingDetails((prev) => ({ ...prev, name: e.target.value }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="John Doe"
              required
            />
          </div>

          {/* Card Element */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Card Information
            </label>
            <div
              className="p-3 border border-gray-300 rounded-md bg-white min-h-[40px] focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500"
              style={{
                transition: "none", // Override global transition
                pointerEvents: "auto",
                position: "relative",
                zIndex: 1,
              }}
            >
              <CardElement
                options={cardElementOptions}
                onReady={() => {
                  console.log("CardElement is ready");
                  setCardReady(true);
                }}
                onChange={(event) => {
                  console.log("CardElement changed:", event);
                  setCardComplete(event.complete);
                  if (event.error) {
                    setError(event.error.message);
                  } else {
                    setError(null);
                  }
                }}
              />
            </div>
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-gray-500">
                Enter your card number, expiry date, and CVC
              </p>
              <div className="flex items-center gap-2">
                {cardReady && (
                  <span className="text-xs text-green-600 flex items-center gap-1">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    Ready
                  </span>
                )}
                {cardComplete && (
                  <span className="text-xs text-blue-600 flex items-center gap-1">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    Complete
                  </span>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Billing Address - Only show for add_payment_method mode */}
      {mode === "add_payment_method" && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address Line 1
            </label>
            <input
              type="text"
              value={billingDetails.address.line1}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, line1: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="123 Main St"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              type="text"
              value={billingDetails.address.city}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, city: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="New York"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              State
            </label>
            <input
              type="text"
              value={billingDetails.address.state}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, state: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="NY"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ZIP Code
            </label>
            <input
              type="text"
              value={billingDetails.address.postal_code}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, postal_code: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="10001"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <select
              value={billingDetails.address.country}
              onChange={(e) =>
                setBillingDetails((prev) => ({
                  ...prev,
                  address: { ...prev.address, country: e.target.value },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="GB">United Kingdom</option>
              {/* Add more countries as needed */}
            </select>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
          disabled={processing || isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={
            processing ||
            isLoading ||
            (mode === "add_payment_method" && (!stripe || !elements)) ||
            (mode === "process_payment" &&
              !paymentMethodId &&
              !selectedPaymentMethodId)
          }
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {processing || isLoading
            ? "Processing..."
            : mode === "process_payment"
              ? `Pay $${amount.toFixed(2)}`
              : !stripe || !elements
                ? "Loading Stripe..."
                : "Add Payment Method"}
        </button>
      </div>
    </form>
  );
};

interface StripePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (result: any) => void;
  mode?: "add_payment_method" | "process_payment";
  amount?: number;
  currency?: string;
  paymentMethodId?: string;
}

const StripePaymentModal: React.FC<StripePaymentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode = "add_payment_method",
  amount = 0,
  currency = "usd",
  paymentMethodId,
}) => {
  if (!isOpen) return null;

  // Check if Stripe key is configured
  if (!stripePublishableKey) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
          <h2 className="text-xl font-semibold mb-4 text-red-600">
            Configuration Error
          </h2>
          <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md mb-4">
            Stripe publishable key is not configured. Please add
            VITE_STRIPE_PUBLISHABLE_KEY to your environment variables.
          </div>
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4">
          {mode === "process_payment"
            ? "Complete Payment"
            : "Add Payment Method"}
        </h2>
        <Elements stripe={stripePromise}>
          <StripePaymentForm
            onSuccess={onSuccess}
            onCancel={onClose}
            mode={mode}
            amount={amount}
            currency={currency}
            paymentMethodId={paymentMethodId}
          />
        </Elements>
      </div>
    </div>
  );
};

export default StripePaymentModal;
