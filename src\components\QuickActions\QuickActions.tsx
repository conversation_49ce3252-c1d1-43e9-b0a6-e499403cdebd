import React from "react";
import { Link } from "react-router-dom";
import { QuickActionsProps } from "./QuickActions.types";

const QuickActions: React.FC<QuickActionsProps> = ({
  title = "Quick Actions",
  actions,
  className = "",
}) => {
  return (
    <div className={`mb-8 ${className}`}>
      <h2 className="text-xl font-bold text-white mb-4">{title}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {actions.map((action) => (
          <div
            key={action.id}
            className="bg-white rounded-lg p-6 flex flex-col h-full"
          >
            <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <img
                src={action.icon}
                alt={action.title}
                className="w-6 h-6 object-cover rounded-lg"
              />
            </div>
            <h3 className="font-bold text-[#0F2C59] mb-2">{action.title}</h3>
            <p className="text-gray-600 text-sm mb-4 flex-grow">
              {action.description}
            </p>
            <Link
              to={action.link}
              className={`inline-block ${
                action.buttonColor || "bg-[#E63946]"
              } text-white px-4 py-2 rounded-[30px] text-sm font-medium hover:opacity-90 transition-colors mt-auto w-full max-w-[126px] text-center`}
            >
              {action.buttonText} →
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;
